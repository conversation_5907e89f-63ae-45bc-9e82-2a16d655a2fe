defmodule Test.Support.CallbackExtension do
  @moduledoc """
  A test extension that demonstrates the new callback-based approach.

  This extension provides custom prepare and validate functionality using
  the new callback import system instead of quoted code injection.
  """

  @behaviour Drops.Operations.Extension

  @impl true
  def enabled?(_opts) do
    # Always enabled when registered
    true
  end

  @impl true
  def __extension_callbacks__ do
    [:prepare, :validate]
  end

  # Extension callback functions that will be imported into operations

  @doc """
  Custom prepare callback that adds a prefix to the name parameter.
  """
  def prepare(%{params: params} = context) do
    updated_params =
      if Map.has_key?(params, :name) do
        Map.put(params, :name, "callback_" <> params.name)
      else
        params
      end

    {:ok, Map.put(context, :params, updated_params)}
  end

  @doc """
  Custom validate callback that ensures name doesn't contain "forbidden".
  """
  def validate(%{params: params} = context) do
    if Map.has_key?(params, :name) and String.contains?(params.name, "forbidden") do
      {:error, "name cannot contain 'forbidden'"}
    else
      {:ok, context}
    end
  end

  # Optional: UnitOfWork extension (still uses the old approach)
  @impl true
  def extend_unit_of_work(uow, _opts) do
    uow
  end
end
