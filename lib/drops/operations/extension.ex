defmodule Drops.Operations.Extension do
  @moduledoc """
  Behaviour for Operations extensions.

  Extensions allow adding functionality to Operations modules based on configuration.
  Extensions can define optional callbacks that get imported into operations, providing
  a cleaner alternative to quoted code injection.

  ## Extension Interface

  Extensions must implement the following callbacks:

  - `enabled?/1` - Determines if the extension should be loaded based on options
  - `__extension_callbacks__/0` - Returns a list of callback functions this extension provides

  Extensions can also define optional callback functions that will be imported into
  operations when the extension is enabled. These callbacks can be overridden by
  the operation module.

  ## Example Extension

      defmodule MyExtension do
        @behaviour Drops.Operations.Extension

        @impl true
        def enabled?(opts) do
          Keyword.has_key?(opts, :my_option)
        end

        @impl true
        def __extension_callbacks__ do
          [:prepare, :validate]
        end

        # Optional callback that can be imported
        def prepare(%{params: params} = context) do
          # Custom prepare logic
          {:ok, context}
        end

        # Optional callback that can be imported
        def validate(%{params: params} = context) do
          # Custom validation logic
          {:ok, context}
        end
      end

  ## Callback Import Process

  When an extension is enabled, its callbacks are imported into the operation module
  using `import MyExtension, only: [prepare: 1, validate: 1]`. The imported functions
  are then made overridable using `defoverridable`, allowing operations to override
  them with custom implementations.

  ## Parameters

  - `opts` - The options passed to the Operations module

  ## Returns

  Returns `true` if the extension should be loaded, `false` otherwise.
  """
  @callback enabled?(opts :: keyword()) :: boolean()

  @doc """
  Returns a list of callback function names that this extension provides.

  This function should return a list of atoms representing the function names
  that this extension defines and wants to make available for import into
  operations. Each function should accept a single context parameter.

  ## Returns

  Returns a list of atoms representing callback function names.

  ## Example

      def __extension_callbacks__ do
        [:prepare, :validate, :execute]
      end
  """
  @callback __extension_callbacks__() :: [atom()]

  @doc """
  Allows extensions to modify the UnitOfWork for an operation.

  This is called after the UnitOfWork is created to allow extensions
  to override specific steps in the processing pipeline.

  ## Parameters

  - `uow` - The UnitOfWork to modify
  - `opts` - The options for the operation

  ## Returns

  Returns the modified UnitOfWork.
  """
  @callback extend_unit_of_work(uow :: Drops.Operations.UnitOfWork.t(), opts :: keyword()) ::
              Drops.Operations.UnitOfWork.t()

  @optional_callbacks extend_unit_of_work: 2

  @doc """
  Get enabled extensions based on the provided options and registered extensions.

  Extensions are enabled if they are registered and their enabled?(opts) callback returns true.

  ## Parameters

  - `registered_extensions` - List of registered extension modules
  - `opts` - The options to check against

  ## Returns

  Returns a list of extension modules that should be enabled.
  """
  def enabled_extensions(registered_extensions, opts) do
    registered_extensions
    |> Enum.filter(fn extension ->
      extension.enabled?(opts)
    end)
    |> Enum.uniq()
  end

  @doc """
  Generate import statements for extension callbacks.

  This function creates the necessary import and defoverridable statements
  to make extension callbacks available in operation modules.

  ## Parameters

  - `registered_extensions` - List of registered extension modules
  - `opts` - The options passed to the Operations module

  ## Returns

  Returns quoted code containing import and defoverridable statements.
  """
  def generate_callback_imports(registered_extensions, opts) do
    enabled_extensions(registered_extensions, opts)
    |> Enum.flat_map(&generate_extension_imports/1)
  end

  @doc """
  Generate import statements for a single extension.

  ## Parameters

  - `extension` - The extension module

  ## Returns

  Returns quoted code for importing the extension's callbacks.
  """
  def generate_extension_imports(extension) do
    if function_exported?(extension, :__extension_callbacks__, 0) do
      callbacks = extension.__extension_callbacks__()

      if Enum.empty?(callbacks) do
        []
      else
        # Create import statement with only the specified callbacks
        import_functions = Enum.map(callbacks, &{&1, 1})

        [
          quote do
            import unquote(extension), only: unquote(import_functions)
            defoverridable unquote(import_functions)
          end
        ]
      end
    else
      # Extension doesn't support callback imports
      []
    end
  end

  @doc """
  Apply UnitOfWork extensions to modify the processing pipeline.

  ## Parameters

  - `uow` - The UnitOfWork to modify
  - `registered_extensions` - List of registered extension modules
  - `opts` - The options for the operation

  ## Returns

  Returns the modified UnitOfWork with extension overrides applied.
  """
  def extend_unit_of_work(uow, registered_extensions, opts) do
    enabled_extensions(registered_extensions, opts)
    |> Enum.reduce(uow, fn extension, acc_uow ->
      if function_exported?(extension, :extend_unit_of_work, 2) do
        extension.extend_unit_of_work(acc_uow, opts)
      else
        acc_uow
      end
    end)
  end
end
